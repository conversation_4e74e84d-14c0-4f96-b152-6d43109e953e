#!/usr/bin/env python3
"""
Gemini MCP Tool HTTP Proxy
Converts stdio-based Gemini MCP Tool to HTTP transport
"""

import asyncio
import json
import logging
import os
from contextlib import AsyncExitStack
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Gemini MCP Tool HTTP Proxy")

# Global MCP session and connection management
mcp_session = None
exit_stack = None


async def get_mcp_session() -> ClientSession:
    """Get or create MCP session with Gemini MCP Tool

    Returns:
        ClientSession: The MCP session

    Raises:
        Exception: If session initialization fails
    """
    global mcp_session, exit_stack

    if mcp_session is not None:
        return mcp_session

    try:
        # Configure Gemini MCP Tool with OAuth
        oauth_file = os.getenv("GEMINI_OAUTH_FILE", "/app/.gemini/oauth_creds.json")

        # Set environment variable for Gemini CLI
        env = os.environ.copy()
        env["GEMINI_OAUTH_FILE"] = oauth_file

        server_params = StdioServerParameters(
            command="npx",
            args=["-y", "gemini-mcp-tool"],
            cwd="/app",
            env=env
        )

        # Create exit stack for proper async context management
        exit_stack = AsyncExitStack()

        # Create session with proper lifecycle management
        stdio_transport = await exit_stack.enter_async_context(stdio_client(server_params))
        read, write = stdio_transport
        session = await exit_stack.enter_async_context(ClientSession(read, write))

        # Initialize the session
        await session.initialize()
        mcp_session = session
        logger.info("✅ Gemini MCP Tool session initialized with OAuth")
        return session

    except Exception as e:
        logger.error(f"❌ Failed to initialize Gemini MCP Tool session: {e}")
        # Clean up on failure
        if exit_stack:
            await exit_stack.aclose()
            exit_stack = None
        raise


async def cleanup_session() -> None:
    """Clean up MCP session and connections"""
    global mcp_session, exit_stack

    if exit_stack:
        try:
            await exit_stack.aclose()
            logger.info("✅ MCP session cleaned up")
        except Exception as e:
            logger.error(f"❌ Error during session cleanup: {e}")
        finally:
            exit_stack = None
            mcp_session = None


@app.on_event("startup")
async def startup_event() -> None:
    """Initialize MCP session on startup"""
    try:
        await get_mcp_session()
    except Exception as e:
        logger.error(f"Failed to initialize MCP session on startup: {e}")


@app.on_event("shutdown")
async def shutdown_event() -> None:
    """Clean up MCP session on shutdown"""
    await cleanup_session()


@app.post("/mcp")
async def handle_mcp_request(request: Request) -> Dict[str, Any]:
    """Handle MCP requests via HTTP

    Args:
        request: The HTTP request containing MCP JSON-RPC payload

    Returns:
        dict: JSON-RPC response

    Raises:
        HTTPException: If request processing fails
    """
    body = None
    try:
        session = await get_mcp_session()
        body = await request.json()

        method = body.get("method")
        params = body.get("params", {})
        request_id = body.get("id")

        # Route to appropriate MCP method
        if method == "initialize":
            # Session is already initialized, return success
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "serverInfo": {"name": "gemini-mcp-tool", "version": "1.0.0"}
                }
            }

        elif method == "tools/list":
            result = await session.list_tools()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"tools": result.tools}
            }

        elif method == "tools/call":
            tool_name = params.get("name")
            tool_args = params.get("arguments", {})

            result = await session.call_tool(tool_name, tool_args)
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result.model_dump()
            }

        elif method == "resources/list":
            result = await session.list_resources()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"resources": result.resources}
            }

        elif method == "resources/read":
            uri = params.get("uri")
            result = await session.read_resource(uri)
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result.model_dump()
            }

        elif method == "prompts/list":
            result = await session.list_prompts()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"prompts": result.prompts}
            }

        elif method == "prompts/get":
            prompt_name = params.get("name")
            prompt_args = params.get("arguments", {})
            result = await session.get_prompt(prompt_name, prompt_args)
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result.model_dump()
            }

        else:
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling MCP request: {e}")
        return {
            "jsonrpc": "2.0",
            "id": body.get("id") if body else None,
            "error": {
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        }


@app.get("/health")
async def health_check() -> Dict[str, str]:
    """Health check endpoint

    Returns:
        dict: Health status information

    Raises:
        HTTPException: If service is unhealthy
    """
    try:
        await get_mcp_session()
        return {"status": "healthy", "service": "gemini-mcp-tool-proxy", "auth": "oauth"}
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {e}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
