{"folders": [{"path": "."}], "settings": {"Lingma.aI Chat.mcpToolsInAgentMode": true, "Lingma.DisplayLanguage": "English", "Lingma.PreferredLanguage for AI Chat": "English", "Lingma.PreferredLanguage forCommitMessage": "English", "redHatDependencyAnalytics.usePythonVirtualEnvironment": true, "redHatDependencyAnalytics.usePipDepTree": true, "redHatDependencyAnalytics.useGoMVS": true}, "extensions": {"recommendations": ["sonarsource.sonarlint-vscode", "rooveterinaryinc.roo-cline", "hen<PERSON><PERSON><PERSON>.docker-linter", "sourcery.sourcery", "ms-vscode-remote.remote-containers", "redhat.fabric8-analytics", "appland.appmap", "sourcegraph.cody-ai", "coder.coder-remote", "continue.continue", "ms-toolsai.datawrangler", "googlecloudtools.cloudcode", "ms-vscode.makefile-tools", "codeium.windsurfpyright", "altimateai.vscode-altimate-mcp-server", "ggml-org.llama-vscode", "kombai.kombai", "alibaba-cloud.tongyi-lingma", "augment.vscode-augment"]}}