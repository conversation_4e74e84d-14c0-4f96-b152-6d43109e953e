#!/usr/bin/env python3
"""
Cipher MCP Server HTTP Proxy
Converts stdio-based Cipher MCP server to HTTP transport
"""

import logging
from fastapi import FastAPI, Request, HTTPException
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import uvicorn
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Optional


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Global MCP session
mcp_session: Optional[ClientSession] = None


async def get_mcp_session() -> ClientSession:
    """Get or create MCP session with Cipher server

    Returns:
        ClientSession: The MCP session

    Raises:
        Exception: If session initialization fails
    """
    global mcp_session

    if mcp_session is not None:
        return mcp_session

    try:
        # Configure Cipher MCP server
        server_params = StdioServerParameters(
            command="npx",
            args=["-y", "cipher", "--mode", "mcp"],
            cwd="/app"
        )

        # Create session
        async with stdio_client(server_params) as (read, write):
            session = ClientSession(read, write)
            await session.initialize()
            mcp_session = session
            logger.info("✅ Cipher MCP server session initialized")
            return session

    except Exception as e:
        logger.error(f"❌ Failed to initialize Cipher MCP session: {e}")
        raise


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Lifespan handler for FastAPI app

    Yields:
        None
    """
    try:
        await get_mcp_session()
        yield
    finally:
        global mcp_session
        if mcp_session:
            mcp_session = None


app = FastAPI(title="Cipher MCP Server HTTP Proxy", lifespan=lifespan)


@app.post("/mcp")
async def handle_mcp_request(request: Request) -> dict[str, Any]:
    """Handle MCP requests via HTTP

    Args:
        request: The incoming HTTP request

    Returns:
        JSON-RPC response

    Raises:
        HTTPException: For unsupported methods or internal errors
    """
    try:
        session = await get_mcp_session()
        body = await request.json()

        method = body.get("method")
        params = body.get("params", {})
        request_id = body.get("id")

        # Route to appropriate MCP method
        if method == "initialize":
            result = await session.initialize()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result
            }

        elif method == "tools/list":
            tools = await session.list_tools()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"tools": tools}
            }

        elif method == "tools/call":
            tool_name = params.get("name")
            tool_args = params.get("arguments", {})

            result = await session.call_tool(tool_name, tool_args)
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result
            }

        elif method == "resources/list":
            resources = await session.list_resources()
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {"resources": resources}
            }

        elif method == "resources/read":
            uri = params.get("uri")
            result = await session.read_resource(uri)
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result
            }

        else:
            raise HTTPException(status_code=400, detail=f"Unsupported method: {method}")

    except Exception as e:
        logger.error(f"Error handling MCP request: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint

    Returns:
        Health status

    Raises:
        HTTPException: If service is unhealthy
    """
    try:
        if mcp_session is None:
            raise RuntimeError("MCP session not initialized")
        return {"status": "healthy", "service": "cipher-mcp-proxy"}
    except RuntimeError as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {e}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8002)
